package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.ConversationReportDTO;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.admin.*;
import com.stepup.springrobot.dto.chat.ConversationLanguagePredictionDTO;
import com.stepup.springrobot.dto.communication.UpdateSDDetailDTO;
import com.stepup.springrobot.model.AIPromptBucket;
import com.stepup.springrobot.model.ModelAIProvider;
import com.stepup.springrobot.model.admin.UpdateSDPath;
import com.stepup.springrobot.model.chat.AICharacter;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.chat.RobotUserConversationRecordHistory;
import com.stepup.springrobot.model.communication.UpdateSDActionType;
import com.stepup.springrobot.model.communication.UpdateSDDataType;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.model.user.ProfileVariableInfo;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.AIPromptBucketRepository;
import com.stepup.springrobot.repository.AIProviderTokensRepository;
import com.stepup.springrobot.repository.admin.UpdateSDPathRepository;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.auth.ProfileVariableInfoRepository;
import com.stepup.springrobot.repository.auth.ProfileVariableRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRecordHistoryRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.communication.ConnectService;
import com.theokanning.openai.completion.chat.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdminService extends CommonService {
    private final UpdateSDPathRepository updateSDPathRepository;
    private final ConnectService connectService;

    private final RobotUserConversationRepository robotUserConversationRepository;
    private final RobotUserConversationRecordHistoryRepository robotUserConversationRecordHistoryRepository;
    private final UserRepository userRepository;
    private final RobotUserRepository robotUserRepository;

    private final ProfileRepository profileRepository;
    private final ProfileVariableInfoRepository profileVariableInfoRepository;
    private final ProfileVariableRepository profileVariableRepository;

    private final ObjectMapper objectMapper;
    private final LLMService llmService;
    private final OpenAIService openAIService;

    private final AIProviderTokensRepository aiProviderTokensRepository;
    private final AIPromptBucketRepository aiPromptBucketRepository;


    @Autowired
    protected AdminService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService,
                           SlackWarningSystemService slackWarningSystemService, UpdateSDPathRepository updateSDPathRepository, ConnectService connectService,
                           RobotUserConversationRepository robotUserConversationRepository,
                           RobotUserConversationRecordHistoryRepository robotUserConversationRecordHistoryRepository,
                           UserRepository userRepository, RobotUserRepository robotUserRepository, ProfileRepository profileRepository,
                           ProfileVariableInfoRepository profileVariableInfoRepository, ProfileVariableRepository profileVariableRepository,
                           ObjectMapper objectMapper1, LLMService llmService, OpenAIService openAIService,
                           AIProviderTokensRepository aiProviderTokensRepository, AIPromptBucketRepository aiPromptBucketRepository) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
        this.updateSDPathRepository = updateSDPathRepository;
        this.connectService = connectService;
        this.robotUserConversationRepository = robotUserConversationRepository;
        this.robotUserConversationRecordHistoryRepository = robotUserConversationRecordHistoryRepository;
        this.userRepository = userRepository;
        this.robotUserRepository = robotUserRepository;
        this.profileRepository = profileRepository;
        this.profileVariableInfoRepository = profileVariableInfoRepository;
        this.profileVariableRepository = profileVariableRepository;
        this.objectMapper = objectMapper1;
        this.llmService = llmService;
        this.openAIService = openAIService;
        this.aiProviderTokensRepository = aiProviderTokensRepository;
        this.aiPromptBucketRepository = aiPromptBucketRepository;
    }

    public DataResponseDTO<?> uploadFile(HttpServletRequest request, MultipartFile multipartFile, String fileName)
            throws IOException {
        String folder = CodeDefine.UPLOAD_MEDIA_IN_BUCKET_STORAGE_S3 + "/" + FilenameUtils.getExtension(multipartFile.getOriginalFilename());
        fileName = StringUtils.isEmpty(fileName) ? FilenameUtils.getBaseName(multipartFile.getOriginalFilename()) + "_" + System.currentTimeMillis() + "." + FilenameUtils.getExtension(multipartFile.getOriginalFilename()) : fileName;

        File file = convertMultiPartFileToFileWithName(multipartFile, fileName);

        if (file.length() == 0 && file.exists()) {
            file.delete();
            log.error("File to be uploaded is empty");
        }

        log.info("Start upload file to S3 {}", file.length());
        // String objectKey = folder + File.separator + file.getName();
        String objectKey = folder + "/" + fileName;

        String tag = uploadFileToS3.putS3ObjectWithoutType(ConfigUtil.INSTANCE.getS3Bucket(), objectKey, file);
        file.delete();

        if (tag != null) {
            log.info("Upload file to S3 success: {}", tag);
        } else {
            log.error("Upload file to S3 fail");
        }

        String userAudio = ConfigUtil.INSTANCE.getCdnDomain() + "/" + objectKey;

        return new DataResponseDTO<>(CodeDefine.OK, "Upload file thành công", userAudio);
    }

    public DataResponseDTO<?> getAllUpdateSDPath(HttpServletRequest request) throws IOException {
        List<UpdateSDPath> updateSDPaths = updateSDPathRepository.findAll();
        return new DataResponseDTO<>(CodeDefine.OK, "Upload file thành công", updateSDPaths);
    }

    public DataResponseDTO<?> updateSD(HttpServletRequest request, UpdateSDReqDTO updateSDReqDTO) throws IOException {
        connectService.handleUpdateSDCardRequest("admin", updateSDReqDTO.getRobotId(), UpdateSDDetailDTO.builder()
                .url(updateSDReqDTO.getUrl())
                .path(updateSDReqDTO.getPath())
                .action(UpdateSDActionType.UPDATE)
                .type(UpdateSDDataType.FILE)
                .build());
        return new DataResponseDTO<>(CodeDefine.OK, "Gửi yêu cầu update SD thành công");
    }

    public DataResponseDTO<?> showFaceDemo(HttpServletRequest request, UpdateSDReqDTO updateSDReqDTO)
            throws IOException {
        connectService.handleFaceDemoRequest("admin", updateSDReqDTO.getRobotId(), updateSDReqDTO.getUrl());
        return new DataResponseDTO<>(CodeDefine.OK, "Gửi yêu cầu update SD thành công");
    }

    public DataResponseDTO<?> getConversationLogs(int page, int size) {
        Page<RobotUserConversation> logs = robotUserConversationRepository
                .findConversationsWithLog(PageRequest.of(page, size));
        List<RobotLogConversationDTO> list = logs.getContent().stream().map(log -> {
            try {
                JsonNode logJson = objectMapper.readValue(log.getLog(), new TypeReference<>() {
                });
                return RobotLogConversationDTO.builder()
                        .conversationId(log.getId())
                        .robotId(log.getUserId())
                        .log(logJson.get(0).get("data"))
                        .build();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

        }).collect(Collectors.toList());

        Page<RobotLogConversationDTO> response = new PageImpl<>(list, logs.getPageable(), logs.getTotalElements());
        return new DataResponseDTO<>(CodeDefine.OK, "Success", response);
    }

    // Add these methods to the existing class
    public Page<RobotUserConversation> findAll(PageRequest pageRequest) {
        return robotUserConversationRepository.findAllByOrderByIdDesc(pageRequest);
    }

    public Page<RobotUserConversation> findAllWithFilters(Long id, String phone, String userId,
                                                          String startDate, String endDate, Long botId, PageRequest pageRequest) {
        if (id != null) {
            RobotUserConversation conversation = findConversationById(id);
            if (conversation != null) {
                return new PageImpl<>(Collections.singletonList(conversation), pageRequest, 1);
            }
            return new PageImpl<>(Collections.emptyList(), pageRequest, 0);
        }

        Specification<RobotUserConversation> spec = Specification.where(null);

        if (StringUtils.isNotBlank(phone)) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("phone")), "%" + phone.toLowerCase() + "%"));
        }

        if (StringUtils.isNotBlank(userId)) {
            spec = spec
                    .and((root, query, cb) -> cb.like(cb.lower(root.get("userId")), "%" + userId.toLowerCase() + "%"));
        }

        if (botId != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("botId"), botId));
        }

        if (StringUtils.isNotBlank(startDate)) {
            try {
                LocalDate localDate = LocalDate.parse(startDate);
                Date start = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(root.get("createdAt"), start));
            } catch (Exception e) {
                log.error("Error parsing start date: {}", e.getMessage());
            }
        }

        if (StringUtils.isNotBlank(endDate)) {
            try {
                LocalDate localDate = LocalDate.parse(endDate);
                Date end = Date.from(localDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                spec = spec.and((root, query, cb) -> cb.lessThan(root.get("createdAt"), end));
            } catch (Exception e) {
                log.error("Error parsing end date: {}", e.getMessage());
            }
        }

        // Create a new PageRequest with sorting by ID in descending order
        PageRequest sortedPageRequest = PageRequest.of(
                pageRequest.getPageNumber(),
                pageRequest.getPageSize(),
                Sort.by("id").descending());

        return robotUserConversationRepository.findAll(spec, sortedPageRequest);
    }

    public RobotUserConversation findConversationById(Long id) {
        return robotUserConversationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Conversation not found with id: " + id));
    }

    public List<RobotUserConversationRecordHistory> findByRobotUserConversationIdOrderByIdAsc(Long conversationId,
                                                                                              PageRequest pageRequest) {

        Page<RobotUserConversationRecordHistory> messages = robotUserConversationRecordHistoryRepository
                .findByRobotUserConversationIdOrderByIdAsc(conversationId,
                        pageRequest);
        List<RobotUserConversationRecordHistory> list = new ArrayList<>(messages.getContent());
        // Rearrange elements: switch positions when FAST_RESPONSE is right before USER
        for (int i = 0; i < list.size() - 1; i++) {
            if (list.get(i).getCharacter() == AICharacter.FAST_RESPONSE
                    && list.get(i + 1).getCharacter() == AICharacter.USER) {
                // Swap the elements
                RobotUserConversationRecordHistory temp = list.get(i);
                list.set(i, list.get(i + 1));
                list.set(i + 1, temp);
            }
        }

        return list;
    }

    public Page<RobotUserConversationRecordHistory> findByRobotUserConversationIdAndIdGreaterThanOrderByIdAsc(
            Long conversationId,
            Long afterId,
            Pageable pageable) {
        return robotUserConversationRecordHistoryRepository
                .findByRobotUserConversationIdAndIdGreaterThanOrderByIdAsc(conversationId, afterId, pageable);
    }

    @Transactional
    public DataResponseDTO<?> mapRobotToPhone(String robotId, String phoneNumber) {
        // Check if the phone number exists in the User table
        User user = userRepository.findByPhone(phoneNumber);
        if (user == null) {
            throw new RuntimeException("Phone number does not exist");
        }

        String userId = user.getId();

        // Delete any existing mappings for this user
        List<RobotUser> existingMappings = robotUserRepository.findByUserId(userId);
        if (!existingMappings.isEmpty()) {
            robotUserRepository.deleteAll(existingMappings);
        }

        // Create a new mapping
        RobotUser robotUser = RobotUser.builder()
                .userId(userId)
                .robotId(robotId)
                .name(user.getPhone()) // Use phone as name for reference
                .build();

        robotUserRepository.save(robotUser);

        return new DataResponseDTO<>(CodeDefine.OK, "Robot successfully mapped to phone number", null);
    }

    public DataResponseDTO<?> getConversationChatHistory(Long conversationId) {
        RobotUserConversation conversation = findConversationById(conversationId);
        List<RobotUserConversationRecordHistory> messages = findByRobotUserConversationIdOrderByIdAsc(conversationId,
                PageRequest.of(0, 200));
        List<RobotConversationHistoryDTO> list = messages.stream()
                .map(message -> RobotConversationHistoryDTO.builder()
                        .audio(message.getAudio())
                        .content(message.getContent())
                        .character(message.getCharacter())
                        .build())
                .collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Success", list);
    }

    public DataResponseDTO<?> getRobotProfileVariables(String robotId) {
        return llmService.handleGetUserProfile(robotId);
    }

    public DataResponseDTO<?> createOrUpdateProfile(String input, JsonNode jsonNodeData) {
        boolean isUserId = userRepository.existsById(input);
        String userId;
        if (isUserId) {
            userId = input;
        } else {
            userId = userRepository.findByPhone(input).getId();
        }

        Map<String, String> data = objectMapper.convertValue(jsonNodeData, new TypeReference<>() {
        });

        List<ProfileVariableInfo> profileVariableInfos = profileVariableInfoRepository.findByRobotId(userId);

        for (Map.Entry<String, String> variable : data.entrySet()) {
            boolean exist = false;
            for (ProfileVariableInfo profileVariableInfo : profileVariableInfos) {
                if (profileVariableInfo.getKey().equals(variable.getKey())) {
                    profileVariableInfo.setValue(variable.getValue());
                    exist = true;
                    break;
                }
            }

            if (!exist) {
                profileVariableInfos.add(ProfileVariableInfo.builder()
                        .robotId(userId)
                        .key(variable.getKey())
                        .value(variable.getValue())
                        .build());
                System.out.println("Add new variable: " + variable);
            }
        }

        return new DataResponseDTO<>(CodeDefine.OK,
                "Update profile thành công", profileVariableInfoRepository.saveAll(profileVariableInfos));
    }


    /**
     * Gọi API n8n để lấy báo cáo cuộc hội thoại
     * Method này chỉ có nhiệm vụ gọi API bên ngoài và trả về kết quả
     */
    public JsonNode getConversationReportFromExternalAPI(Long conversationId) {
        try {
            String url = "https://n8n.hacknao.edu.vn/webhook/0bc550d7-b02b-4ce4-9732-d0c1563039be";

            // Create JSON for request body
            String jsonBody = String.format("{\"conversation_id\":\"%d\"}", conversationId);

            // Set up OkHttp client with extended timeouts
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            // Create request body - using correct syntax for OkHttp
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, jsonBody);

            // Create request
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Execute the request
            log.info("Sending request to external API with body: {}", jsonBody);
            Response response = client.newCall(request).execute();

            // Check if response is successful
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No response body";
                log.error("HTTP error from API: {} - {}", response.code(), errorBody);
                throw new RuntimeException("Failed to fetch conversation report from external API");
            }

            // Get response body
            String responseBody = response.body().string();
            log.info("API Response status: {}", response.code());

            if (responseBody == null || responseBody.isEmpty()) {
                throw new RuntimeException("No response body from external API");
            }

            // Parse response
            JsonNode responseJson = objectMapper.readTree(responseBody);
            JsonNode content = responseJson
                    .path("choices")
                    .path(0)
                    .path("message")
                    .path("content")
                    .path("conversation");

            if (content.isMissingNode() || content.isNull()) {
                log.error("Content not found in API response");
                return null;
            }

            // Try to parse the content as JSON if it's a string
            JsonNode contentJson;
            if (content.isTextual()) {
                try {
                    contentJson = objectMapper.readTree(content.asText());
                } catch (Exception e) {
                    log.error("Error parsing content as JSON: {}", e.getMessage());
                    contentJson = content;
                }
            } else {
                contentJson = content;
            }

            return contentJson;
        } catch (java.io.IOException e) {
            log.error("IO error fetching conversation data from external API: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("Error fetching conversation data from external API: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Lấy báo cáo cuộc hội thoại theo ID, trả về đối tượng ContentDTO cho view
     * Method này sử dụng cho ModelAndView
     */
    @Transactional
    public ConversationReportContentDTO getConversationReportContentById(Long conversationId, List<String> groupMessagesInput) {
        try {
            List<String> groupMessages = groupMessagesInput;
            if (groupMessagesInput == null) {

                // Check if conversation already has report data using the optimized query
                String existingReport = robotUserConversationRepository.findConversationReportsById(conversationId);

                if (existingReport != null && !existingReport.isEmpty()) {
                    log.info("Found existing report for conversation ID: {}", conversationId);
                    return objectMapper.readValue(existingReport, ConversationReportContentDTO.class);
                }

                // If no report exists, make sure the conversation exists before calling API
                if (!robotUserConversationRepository.existsById(conversationId)) {
                    throw new RuntimeException("Conversation not found with id: " + conversationId);
                }

                groupMessages = convertConversationToList(conversationId);
            }

            List<CompletableFuture<JsonNode>> futures = groupMessages.stream()
                    .map(message -> CompletableFuture.supplyAsync(() -> processChatGPTGenerateReport(message)))
                    .collect(Collectors.toList());

            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            allFutures.join();

            List<JsonNode> results = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            ConversationReportContentDTO conversationReportContentDTO = convertConversationFormat(objectMapper.valueToTree(results));

            if (groupMessagesInput == null) {
                robotUserConversationRepository.updateConversationReports(conversationId, objectMapper.writeValueAsString(conversationReportContentDTO));
            }

            return convertConversationFormat(objectMapper.valueToTree(results));
        } catch (Exception e) {
            log.error("Error retrieving conversation report: {}", e.getMessage(), e);
            return new ConversationReportContentDTO(); // Return empty object instead of null
        }
    }

    private JsonNode processChatGPTGenerateReport(String message) {
        AIPromptBucket prompt = aiPromptBucketRepository.findByName("generate_conversation_report")
                .orElseThrow(() -> new RuntimeException("ChatGPT prompt bucket not found"));

        String systemPrompt = prompt.getPrompt();

        String userPrompt = "Phân tích cuộc hội thoại sau theo các tiêu chí đã cho:\n\n" + message;

        List<ChatMessage> messages = new ArrayList<>();
        messages.add(new ChatMessage("system", systemPrompt));
        messages.add(new ChatMessage("user", userPrompt));

        String openAIToken = aiProviderTokensRepository.findByProvider(ModelAIProvider.openai).get(0).getToken();
        String chatResponse = openAIService.getOpenAIChatResponse(openAIToken, prompt.getModel(), messages);
        try {
            return objectMapper.readTree(chatResponse).get("conversation").get(0);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public List<String> convertConversationToList(Long conversationId) {
        List<ConversationReportDTO> reportData = robotUserConversationRecordHistoryRepository.findConversationReportByConversationId(conversationId);

        if (reportData == null || reportData.isEmpty()) {
            throw new RuntimeException("No conversation report data found for conversation ID: " + conversationId);
        }

        List<String> result = new ArrayList<>();

        // Create the first group with initial messages
        StringBuilder currentGroup = new StringBuilder();
        boolean foundUser = false;

        // Process all messages in a single loop
        for (int i = 0; i < reportData.size(); i++) {
            ConversationReportDTO currentMessage = reportData.get(i);
            String currentElement = currentMessage.getCharacter() + ": " + currentMessage.getContent();

            // If we found a USER message after a previous USER message, start a new group
            if (AICharacter.USER.equals(currentMessage.getCharacter()) && foundUser && i > 0) {
                // Add the completed group to results
                currentGroup.append("| ").append(currentElement);
                result.add(currentGroup.toString());

                // Start a new group with the current USER message
                currentGroup = new StringBuilder("| " + currentElement);
            } else {
                // Add the current message to the current group
                currentGroup.append("| ").append(currentElement);

                // Mark if we found a USER message
                if (AICharacter.USER.equals(currentMessage.getCharacter())) {
                    foundUser = true;
                }
            }
        }

        // Add the final group if it's not empty
        if (currentGroup.length() > 0) {
            result.add(currentGroup.toString());
        }

        return result;
    }

    public ConversationReportContentDTO convertConversationFormat(JsonNode originalJson) {
        List<String> keys = new ArrayList<>();
        List<ConversationReportDataItemDTO> dataItems = new ArrayList<>();

        // Nếu originalJson không phải là mảng, trả về kết quả rỗng
        if (!originalJson.isArray() || originalJson.isEmpty()) {
            return new ConversationReportContentDTO(keys, dataItems);
        }

        // Lấy phần tử đầu tiên và trích xuất tất cả các keys
        JsonNode firstItem = originalJson.get(0);
        if (firstItem.isObject()) {
            firstItem.fieldNames().forEachRemaining(keys::add);
        }

        // Xử lý từng phần tử trong mảng gốc
        for (JsonNode item : originalJson) {
            List<ConversationReportItemDTO> contentItems = new ArrayList<>();

            // Duyệt qua tất cả các fields trong phần tử hiện tại
            for (String key : keys) {
                JsonNode value = item.get(key);

                // Nếu giá trị là null, thêm text rỗng và is_pass = null
                if (value == null || value.isNull()) {
                    contentItems.add(ConversationReportItemDTO.builder()
                            .text("")
                            .isPass(null)
                            .build());
                } else {
                    String textValue = value.asText();
                    Boolean isPass = null;

                    // Xác định is_pass dựa trên các điều kiện
                    if (textValue.contains("Pass") && !textValue.contains("Không pass")) {
                        isPass = true;
                    } else if (textValue.contains("Không pass") || textValue.equals("false")) {
                        isPass = false;
                    }

                    contentItems.add(ConversationReportItemDTO.builder()
                            .text(textValue)
                            .isPass(isPass)
                            .build());
                }
            }

            dataItems.add(ConversationReportDataItemDTO.builder()
                    .content(contentItems)
                    .build());
        }

        return ConversationReportContentDTO.builder()
                .keys(keys)
                .data(dataItems)
                .build();
    }

    public byte[] generateUserMessagesExcel(Long conversationId) throws IOException {
        // Get all messages for the conversation
        Page<RobotUserConversationRecordHistory> messagesPage = robotUserConversationRecordHistoryRepository
                .findByRobotUserConversationIdOrderByIdAsc(conversationId, PageRequest.of(0, Integer.MAX_VALUE));

        List<RobotUserConversationRecordHistory> messages = messagesPage.getContent();

        // Filter only user messages
        List<RobotUserConversationRecordHistory> userMessages = messages.stream()
                .filter(msg -> msg.getCharacter() == AICharacter.USER)
                .collect(Collectors.toList());

        // Create workbook and sheet
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("User Messages");

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("Timestamp");
        headerRow.createCell(1).setCellValue("Message");
        headerRow.createCell(2).setCellValue("Audio Link");

        // Create data rows
        int rowNum = 1;
        for (RobotUserConversationRecordHistory message : userMessages) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(message.getCreatedAt().toString());
            row.createCell(1).setCellValue(message.getContent());
            row.createCell(2).setCellValue(message.getAudio() != null ? message.getAudio() : "");
        }

        // Auto-size columns
        for (int i = 0; i < 3; i++) {
            sheet.autoSizeColumn(i);
        }

        // Convert workbook to byte array
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return outputStream.toByteArray();
    }

    public DataResponseDTO<Object> getConversationPredictLanguage(String conversationId) {
        Long convId = Long.parseLong(conversationId);

        // Fetch only required fields from conversation records
        List<Object[]> recordsData = robotUserConversationRecordHistoryRepository
                .findConversationLanguagePrediction(convId);

        List<ConversationLanguagePredictionDTO> dtos = recordsData.stream()
                .map(record -> ConversationLanguagePredictionDTO.builder()
                        .content((String) record[1])
                        .language((String) record[2])
                        .character((String) record[3])
                        .audio((String) record[0])
                        .build())
                .collect(Collectors.toList());

        // Create result data structure
        List<Map<String, Object>> resultData = new ArrayList<>();

        // Process DTOs to build the conversation flow
        for (int i = 0; i < dtos.size(); i++) {
            ConversationLanguagePredictionDTO dto = dtos.get(i);

            // If it's a BOT_RESPONSE_CONVERSATION record
            if (dto.getCharacter() != null &&
                    dto.getCharacter().equals("BOT_RESPONSE_CONVERSATION")) {

                Map<String, Object> row = new HashMap<>();
                row.put("Câu pika nói", dto.getContent());
                row.put("Language", dto.getLanguage());

                // Look for the next USER response and audio
                String userResponse = "";
                String userAudio = null;

                if (i + 1 < dtos.size() &&
                        dtos.get(i + 1).getCharacter() != null &&
                        dtos.get(i + 1).getCharacter().equals("USER")) {

                    ConversationLanguagePredictionDTO userDto = dtos.get(i + 1);
                    userResponse = userDto.getContent();
                    userAudio = userDto.getAudio(); // Get user's audio
                }

                row.put("Audio", userAudio); // Use user's audio data
                row.put("User response", userResponse);
                resultData.add(row);
            }
        }

        return new DataResponseDTO<>(200, "Conversation language prediction data retrieved successfully", resultData);
    }

    /**
     * Lấy thông tin summary của conversation mới nhất theo user_id và bot_id
     * @param userId ID của user
     * @param botId ID của bot
     * @return DataResponseDTO chứa thông tin summary
     */
    public DataResponseDTO<?> getConversationSummary(String userId, Long botId) {
        try {
            // Tìm conversation mới nhất theo userId và botId
            RobotUserConversation conversation = robotUserConversationRepository.findFirstByUserIdAndBotIdOrderByIdDesc(userId, botId);

            if (conversation == null) {
                return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Không tìm thấy cuộc hội thoại nào cho user_id: " + userId + " và bot_id: " + botId, null);
            }

            // Lấy summary từ conversation
            String summary = objectMapper.readTree(conversation.getConversationSummary())
                    .get("result").get("result").asText();

            if (summary == null || summary.trim().isEmpty()) {
                return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Cuộc hội thoại chưa có summary", null);
            }

            // Tạo response data
            ConversationSummaryResDTO responseData = ConversationSummaryResDTO.builder()
                    .conversationId(conversation.getId())
                    .summary(summary)
                    .createdAt(conversation.getCreatedAt())
                    .build();

            return new DataResponseDTO<>(CodeDefine.OK, "Lấy thông tin summary thành công", responseData);

        } catch (Exception e) {
            log.error("Lỗi khi lấy conversation summary cho userId: {} và botId: {}", userId, botId, e);
            return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Lỗi hệ thống: " + e.getMessage(), null);
        }
    }
}
