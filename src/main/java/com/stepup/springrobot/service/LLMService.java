package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.llm.*;
import com.stepup.springrobot.dto.llm_conversation.UpdateUserProfileReqDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.user.AnonymousAccessException;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.llm.LlmImage;
import com.stepup.springrobot.model.llm.LlmMood;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.model.user.Profile;
import com.stepup.springrobot.model.user.ProfileVariable;
import com.stepup.springrobot.model.user.ProfileVariableInfo;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.auth.ProfileVariableInfoRepository;
import com.stepup.springrobot.repository.auth.ProfileVariableRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.llm.LlmImageRepository;
import com.stepup.springrobot.repository.llm.LlmMoodRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LLMService extends CommonService {
    @Value("${llm_communicate_token}")
    private String llmToken;

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileVariableInfoRepository profileVariableInfoRepository;

    @Autowired
    private ProfileVariableRepository profileVariableRepository;

    @Autowired
    private LlmMoodRepository llmMoodRepository;

    @Autowired
    private LlmImageRepository llmImageRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    protected LLMService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getUserProfile(HttpServletRequest request, String conversationId, String token) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        RobotUserConversation conversation = robotUserConversationRepository.findByExternalConversationId(conversationId);
        if (conversation == null) {
            throw new ContentNotFoundException("hội thoại LLM", conversationId);
        }

        return handleGetUserProfile(conversation.getRobotId());
    }

    public DataResponseDTO<?> handleGetUserProfile(String robotId) {
        String name = null;
        RobotUser robotUser = robotUserRepository.findFirstByRobotIdOrderByIdDesc(robotId);
        if (robotUser != null) {
            Profile profile = profileRepository.getCurrentProfileByUserId(robotUser.getUserId());
            if (profile != null) {
                name = profile.getName();
            }
        }

        ObjectNode objectNode = objectMapper.createObjectNode();
        if (name != null) {
            objectNode.put("name", name);
        }

        List<ProfileVariableInfo> profileVariableInfos = profileVariableInfoRepository.findByRobotId(robotId);
        for (ProfileVariableInfo profileVariableInfo : profileVariableInfos) {
            objectNode.put(profileVariableInfo.getKey(), profileVariableInfo.getValue());
        }

        List<ProfileVariable> profileVariables = profileVariableRepository.findAll();
        for (ProfileVariable variable : profileVariables) {
            if (!objectNode.has(variable.getKey())) {
                objectNode.set(variable.getKey(), null);
            }
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy user profile thành công", objectNode);
    }

    public DataResponseDTO<?> getUserProfileDescription(HttpServletRequest request, String token) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        List<ProfileVariable> profileVariables = profileVariableRepository.findAll();
        Map<String, String> variables = new HashMap<>();
        for (ProfileVariable variable : profileVariables) {
            variables.put(variable.getKey(), variable.getDescription());
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy user profile description thành công",
                variables);
    }

    public DataResponseDTO<?> updateUserProfile(HttpServletRequest request, UpdateUserProfileReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !reqDTO.getToken().equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        RobotUserConversation conversation = robotUserConversationRepository.findByExternalConversationId(reqDTO.getConversationId());
        if (conversation == null) {
            throw new ContentNotFoundException("hội thoại LLM", reqDTO.getConversationId());
        }

        List<ProfileVariableInfo> profileVariableInfos = profileVariableInfoRepository.findByRobotId(conversation.getRobotId());

        for (Map.Entry<String, String> variable : reqDTO.getData().entrySet()) {
            boolean exist = false;
            for (ProfileVariableInfo profileVariableInfo : profileVariableInfos) {
                if (profileVariableInfo.getKey().equals(variable.getKey())) {
                    profileVariableInfo.setValue(variable.getValue());
                    exist = true;
                    break;
                }
            }

            if (!exist) {
                profileVariableInfos.add(ProfileVariableInfo.builder()
                        .robotId(conversation.getRobotId())
                        .key(variable.getKey())
                        .value(variable.getValue())
                        .build());
            }
        }

        profileVariableInfoRepository.saveAll(profileVariableInfos);

        return new DataResponseDTO<>(CodeDefine.OK, "Update user profile thành công");
    }

    public DataResponseDTO<?> getLLMImageByBotId(HttpServletRequest request, String token, Long botId) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        List<LlmImage> llmImages = findLlmImageByBotId(botId);
        List<LlmImageDTO> llmImageDTOS = llmImages.stream().map(llmImage -> LlmImageDTO.builder()
                .image(llmImage.getImage())
                .description(llmImage.getDescription())
                .build()).collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách image theo bot id",
                LlmImageResDTO.builder().images(llmImageDTOS).build());
    }

    private List<LlmImage> findLlmImageByBotId(Long botId) {
        RMapCache<String, String> conversationMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_LLM_IMAGE);
        String key = "bot_id_" + botId;
        if (conversationMapCache.containsKey(key)) {
            try {
                return objectMapper.readValue(conversationMapCache.get(key), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Lỗi lấy llm image theo botId từ Redis: {}", e.getMessage());
                conversationMapCache.removeAsync(key);
            }
        }

        List<LlmImage> llmImages = llmImageRepository.findByBotId(botId);
        if (!CollectionUtils.isEmpty(llmImages)) {
            try {
                conversationMapCache.putAsync(key, objectMapper.writeValueAsString(llmImages), CodeDefine.TTL_KEY_FIX_CONTENT, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("Lỗi lưu llm image theo botId vào Redis: {}", e.getMessage());
                conversationMapCache.removeAsync(key);
            }
        }

        return llmImages;
    }

    @Transactional
    public DataResponseDTO<?> saveLLMImagesByBotId(HttpServletRequest request, LlmImageReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !reqDTO.getToken().equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        llmImageRepository.deleteByBotId(reqDTO.getBotId());

        List<LlmImage> llmImages = reqDTO.getImages().stream()
                .map(image -> LlmImage.builder()
                        .image(image.getImage())
                        .description(image.getDescription())
                        .botId(reqDTO.getBotId())
                        .build())
                .collect(Collectors.toList());
        llmImageRepository.saveAll(llmImages);
        redissonClient.getMapCache(CodeDefine.REDIS_KEY_LLM_IMAGE).delete();

        return new DataResponseDTO<>(CodeDefine.OK, "Lưu danh sách image theo bot id thành công",
                LlmImageResDTO.builder().images(reqDTO.getImages()).build());
    }

    public DataResponseDTO<?> getLLMMood(HttpServletRequest request, String token) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        List<LlmMood> llmMoods = findLlmMood();
        List<LlmMoodDTO> llmImageDTOS = llmMoods.stream()
                .map(llmImage -> LlmMoodDTO.builder()
                        .moodName(llmImage.getMoodName())
                        .description(llmImage.getDescription())
                        .example(llmImage.getExample())
                        .build())
                .collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách mood thành công",
                LlmMoodResDTO.builder().moods(llmImageDTOS).build());
    }

    private List<LlmMood> findLlmMood() {
        RBucket<String> conversationMapCache = redissonClient.getBucket(CodeDefine.REDIS_KEY_LLM_MOOD);
        if (conversationMapCache.isExists()) {
            try {
                return objectMapper.readValue(conversationMapCache.get(), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Lỗi lấy llm mood từ Redis: {}", e.getMessage());
                conversationMapCache.delete();
            }
        }

        List<LlmMood> llmMoods = llmMoodRepository.findAll();
        if (!CollectionUtils.isEmpty(llmMoods)) {
            try {
                conversationMapCache.setAsync(objectMapper.writeValueAsString(llmMoods), CodeDefine.TTL_KEY_FIX_CONTENT, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("Lỗi lưu llm mood vào Redis: {}", e.getMessage());
                conversationMapCache.delete();
            }
        }

        return llmMoods;
    }

    @Transactional
    public DataResponseDTO<?> saveLLMMood(HttpServletRequest request, LlmMoodReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !reqDTO.getToken().equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        llmMoodRepository.truncateTable();

        List<LlmMood> llmMoods = reqDTO.getMoods().stream()
                .map(mood -> LlmMood.builder()
                        .moodName(mood.getMoodName())
                        .description(mood.getDescription())
                        .example(mood.getExample())
                        .build())
                .collect(Collectors.toList());
        llmMoodRepository.saveAll(llmMoods);
        redissonClient.getBucket(CodeDefine.REDIS_KEY_LLM_MOOD).delete();

        return new DataResponseDTO<>(CodeDefine.OK, "Lưu danh sách moods thành công",
                LlmMoodResDTO.builder().moods(reqDTO.getMoods()).build());
    }
}
